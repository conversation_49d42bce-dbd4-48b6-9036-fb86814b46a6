import React, { useEffect, useState } from 'react'
import { Platform, StyleSheet, Text, TextInput, TextInputProps, TextStyle, View, ViewStyle } from 'react-native'
import Animated, { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'
import { colors } from '@src/theme/colors'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useBounceOnFocus } from '@src/components/_atoms/_animations/useBounceOnFocus'
import { useFadeLabel } from '@src/components/_atoms/_animations/useFadeLabel'
import { fontFamily } from '@src/theme/typography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ErrorText } from '@src/components/_atoms/_inputs/ErrorText/ErrorText'
import { SeaLabel } from '@src/components/_molecules/SeaLabels/SeaLabel'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

export type Currency = 'NZD' | 'AUD' | 'GBP' | 'USD'

const getCurrencySymbol = (currency: string): string => {
  switch (currency.toUpperCase()) {
    case 'NZD':
    case 'AUD':
    case 'USD':
      return '$'
    case 'GBP':
      return '£'
    default:
      return '$'
  }
}

interface SeaCurrencyInputProps {
  value: number
  currency: Currency
  onChange: (value: number) => void
  label?: string
  hasError?: boolean
  errorText?: string
  style?: ViewStyle
  disabled?: boolean // Optional prop to disable the input
  showIcon?: boolean
  labelIconOptions?: SeaIconProps
}

export const SeaCurrencyInput: React.FC<SeaCurrencyInputProps> = ({
  value,
  currency,
  onChange,
  label,
  hasError = false,
  errorText,
  style,
  disabled = false, // Default to false if not provided
  showIcon = false,
  labelIconOptions,
  ...otherProps
}) => {
  const { styles, theme } = useStyles(styleSheet)
  // Store user input as raw text while typing
  const [textValue, setTextValue] = useState(value > 0 ? value.toFixed(2) : '')

  const handleChange = (text: string) => {
    // Allow only valid number input (digits + one decimal point)
    const sanitizedText = text.replace(/[^0-9.]/g, '')

    // Prevent multiple decimals
    if (sanitizedText.split('.').length > 2) {
      return
    }

    setTextValue(sanitizedText)
  }

  const handleBlur = () => {
    // Convert the input to a fixed 2 decimal format when leaving the field
    const numValue = parseFloat(textValue)
    if (!isNaN(numValue)) {
      const formattedValue = numValue.toFixed(2)
      setTextValue(formattedValue)
      onChange(numValue)
    } else {
      setTextValue('')
      onChange(0)
    }
  }

  const platformOverrides = Platform.select({
    web: { outline: 'none' } as TextStyle,
  })

  return (
    <View style={[styles.container, style]}>
      <SeaLabel showIcon={showIcon} iconOptions={labelIconOptions}>
        {label}
      </SeaLabel>

      <View
        style={[
          styles.inputContainer,
          hasError && styles.inputContainerError,
          disabled
            ? {
                opacity: 0.5,
                backgroundColor: theme.colors.input.disabledBackground,
              }
            : {},
        ]}>
        <View style={styles.currencySymbolContainer}>
          <Text style={styles.currencySymbolText}>{getCurrencySymbol(currency)}</Text>
        </View>
        <TextInput
          value={textValue}
          onChangeText={handleChange}
          placeholder={label}
          keyboardType="decimal-pad"
          placeholderTextColor={colors.text.placeholder}
          onBlur={handleBlur}
          {...otherProps}
          style={[styles.textInputStyle, platformOverrides]}
          editable={!disabled}
        />
      </View>

      <ErrorText hasError={hasError} text={errorText} />
    </View>
  )
}

const SINGLE_LINE_HEIGHT = 40
const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
  },
  labelContainer: {
    marginBottom: 4,
    height: 12,
  },
  inputContainer: {
    borderRadius: 4,
    borderWidth: 1,
    paddingHorizontal: 12,
    height: SINGLE_LINE_HEIGHT,
    width: '100%',
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.background,
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputContainerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  currencySymbolContainer: {
    marginRight: 16,
  },
  currencySymbolText: {
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 16,
    letterSpacing: 0.15,
    fontWeight: '400',
    color: colors.text.primary,
  },
  textInputStyle: {
    height: SINGLE_LINE_HEIGHT,
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 15,
    lineHeight: 18,
    letterSpacing: 0.15,
    fontWeight: '400',
    width: '100%',
    color: colors.text.primary,
    paddingVertical: 8,
  },
}))

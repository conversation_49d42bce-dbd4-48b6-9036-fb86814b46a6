import React, { useState } from 'react'
import {
  CheckBoxActions,
  SeaSelectModal,
  SimpleSelectionData,
  TabularSelectionData,
} from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ErrorText } from '@src/components/_atoms/_inputs/ErrorText/ErrorText'
import { SeaLabel } from '@src/components/_molecules/SeaLabels/SeaLabel'

const ROW_HEIGHT = 40
export interface SeaSelectInputProps<T> {
  disabled?: boolean
  label?: string
  labelIconOptions?: SeaIconProps
  showIcon?: boolean
  data: SimpleSelectionData | TabularSelectionData[]
  /** Use this only when isMulti is true */
  onItemSelect?: (action: CheckBoxActions, changedValue: string) => void
  /** Use this only when isMulti is false */
  onSetItems?: (action: CheckBoxActions, changedValues: string[]) => void
  selectedItemValues?: string[]
  primaryActionOnPress?: () => void
  style?: StyleProp<ViewStyle>
  isMulti?: boolean
  showSelectAllOption?: boolean
  showSearch?: boolean
  inputTextProp?: string
  textInputStyle?: StyleProp<ViewStyle>
  modalTitle?: string
  hasError?: boolean
  errorText?: string
  noValidation?: boolean
  forCustomForm?: boolean // Optional prop for custom form usage
  allSelectedText?: string // Optional prop for all selected text
}

export const SeaSelectInput = <T,>({
  data,
  disabled = false,
  // TODO: Change this to be `FALSE` by default
  isMulti = true,
  inputTextProp,
  label = '',
  labelIconOptions,
  showIcon,
  modalTitle,
  onItemSelect,
  onSetItems,
  primaryActionOnPress,
  showSelectAllOption = true,
  showSearch = false,
  selectedItemValues = [],
  hasError = false,
  errorText,
  style,
  textInputStyle,
  noValidation = false,
  forCustomForm = false, // Default to false if not provided
  allSelectedText, // Optional prop for all selected text
}: SeaSelectInputProps<T>) => {
  // Hooks
  const [viewModal, setViewModal] = useState(false)
  const { styles, theme } = useStyles(styleSheet)

  // Input Text field's value
  const inputText = inputTextProp ?? getDefaultInputText(data, selectedItemValues, allSelectedText)

  const closeModal = () => setViewModal(false)

  const modalTitleText = modalTitle ?? label

  return (
    <>
      <View style={[styles.container, style]}>
        {!!label && (
          <SeaLabel showIcon={showIcon} iconOptions={labelIconOptions}>
            {label.toUpperCase()}
          </SeaLabel>
        )}
        <View
          style={[
            styles.dropdownContainer,
            disabled
              ? {
                  opacity: 0.5,
                }
              : {},
          ]}>
          <TouchableOpacity style={{ overflow: 'hidden' }} onPress={() => setViewModal(!viewModal)} disabled={disabled}>
            <SeaStack
              justify={'between'}
              style={[
                styles.stack,
                hasError && styles.stackError,
                textInputStyle,
                disabled
                  ? {
                      backgroundColor: theme.colors.input.disabledBackground,
                    }
                  : {},
                forCustomForm && disabled ? { height: 'auto', minHeight: ROW_HEIGHT } : {},
              ]}>
              <SeaTypography
                variant={'input'}
                numberOfLines={forCustomForm && disabled ? undefined : 1}
                ellipsizeMode={forCustomForm && disabled ? undefined : 'tail'}
                containerStyle={{ width: '95%' }}>
                {inputText ?? 'Select an Item'}
              </SeaTypography>
              {!forCustomForm && !disabled && <SeaIcon icon={viewModal ? 'arrow_drop_up' : 'arrow_drop_down'} />}
            </SeaStack>
          </TouchableOpacity>

          {/* Multi select - Modal Popup */}
          {isMulti ? (
            <SeaSelectModal
              title={modalTitleText}
              data={data}
              selectedItemValues={selectedItemValues}
              visible={viewModal}
              onClose={closeModal}
              onItemSelect={onItemSelect}
              primaryActionLabel={'OK'}
              primaryActionOnPress={primaryActionOnPress}
              onSetItems={onSetItems}
              showSelectAllOption={showSelectAllOption}
              showSearch={showSearch}
            />
          ) : (
            <SeaSelectModal
              title={modalTitleText}
              data={data}
              selectedItemValues={selectedItemValues}
              visible={viewModal}
              onClose={closeModal}
              onItemSelect={(action, changedValue) => {
                // Update the entire item list instead of the single item
                onSetItems && onSetItems(action, [changedValue])
                closeModal()
              }}
              showPrimaryAction={false}
              showSelectAllOption={false}
              showSearch={showSearch}
            />
          )}
        </View>

        {!noValidation ? <ErrorText hasError={hasError} text={errorText} /> : <></>}
      </View>
    </>
  )
}

const getDefaultInputText = (
  data: SimpleSelectionData | TabularSelectionData[],
  selectedItemValues?: string[],
  allSelectedText?: string
) => {
  if (selectedItemValues?.length === data?.length) {
    return allSelectedText ?? 'All Selected'
  }

  return data
    ?.map(dataItem => {
      // TODO: Cater for TabularSelectionData
      if (selectedItemValues?.includes(dataItem.value)) {
        return dataItem.label ?? dataItem.value
      }
      return undefined
    })
    .filter(a => a !== undefined)
    .join(', ')
}

const styleSheet = createStyleSheet(theme => ({
  container: { flex: 1 },
  dropdownContainer: {},
  stack: {
    height: ROW_HEIGHT,
    borderRadius: 8,
    borderWidth: 1,
    width: '100%',
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.background,
    minWidth: 100,
    paddingHorizontal: 16,
  },
  stackError: {
    borderColor: theme.colors.status.errorPrimary,
  },
}))

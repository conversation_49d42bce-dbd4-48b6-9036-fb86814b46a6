import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_molecules/SeaDrawer/SeaDrawer'
import { EquipmentManualDocument } from '@src/shared-state/VesselDocuments/equipmentManualDocuments'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions, SimpleSelectionData } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import Yup from '@src/lib/yup'
import { preventMultiTap } from '@src/lib/util'
import {
  UpdateEquipmentManualsDto,
  UpdateEquipmentManualsUseCase,
} from '@src/domain/use-cases/maintenance/UpdateEquipmentManualsUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  CreateEquipmentManualsDto,
  CreateEquipmentManualsUseCase,
} from '@src/domain/use-cases/maintenance/CreateEquipmentManualsUseCase'
import { SeaFile } from '@src/lib/fileImports'
import { DrawerContent } from '@src/components/_molecules/SeaDrawer/DrawerContent'
import { DrawerPrimaryAction } from '@src/components/_molecules/SeaDrawer/DrawerButtons'
import { DrawerRow } from '@src/components/_molecules/SeaDrawer/DrawerRow'

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
})

export interface EditEquipmentManualDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: EquipmentManualDocument
  mode?: DrawerMode
}

export function EditEquipmentManualDrawer({
  selectedItem,
  visible,
  onClose,
  mode = DrawerMode.Edit,
  style,
}: EditEquipmentManualDrawerProps) {
  const equipment = sharedState.equipment.use()
  const userId = sharedState.userId.use(visible ? 1 : 500)
  const licenseeId = sharedState.licenseeId.use(visible ? 1 : 500)
  const vesselId = sharedState.vesselId.use(visible ? 1 : 500)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      equipment: selectedItem?.equipmentIds ?? [],
    }
  }, [selectedItem])

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  const handleSubmit = useCallback(
    (values: any) => {
      if (preventMultiTap('equipmentManual')) {
        return
      }

      if (!vesselId || !userId || !licenseeId) {
        console.error('Vessel ID, user ID or licensee ID is not defined')
        return
      }

      const commonDto = {
        title: values.title,
        files: files,
        vesselId,
      }

      const equipmentIdsToRemove: string[] = []
      const equipmentIdsToAdd: string[] = []

      equipment?.all.forEach(item => {
        const itemHasManual = item.equipmentDocumentIds?.includes(selectedItem?.id ?? '')

        if (values.equipment && !values.equipment.includes(item.id)) {
          if (!itemHasManual) {
            equipmentIdsToAdd.push(item.id)
          }
        } else {
          if (itemHasManual && mode === DrawerMode.Edit) {
            equipmentIdsToRemove.push(item.id)
          }
        }
      })

      if (mode === DrawerMode.Create) {
        const equipmentManualsDto: CreateEquipmentManualsDto = {
          ...commonDto,
          equipmentIdsToAdd,
        }

        const createEquipmentManuals = services.get(CreateEquipmentManualsUseCase)

        createEquipmentManuals
          .execute(equipmentManualsDto as CreateEquipmentManualsDto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
      } else {
        const equipmentManualsDto: UpdateEquipmentManualsDto = {
          ...commonDto,
          equipmentIdsToAdd,
          equipmentIdsToRemove,
          id: selectedItem?.id ?? '',
        }
        const updateEquipmentManuals = services.get(UpdateEquipmentManualsUseCase)

        updateEquipmentManuals
          .execute(equipmentManualsDto as UpdateEquipmentManualsDto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
      }
    },
    [selectedItem, equipment, vesselId, userId, licenseeId, mode, files]
  )

  const equipmentOptions = useMemo(() => {
    if (equipment) {
      const options: SimpleSelectionData = []
      equipment.all.forEach(item => {
        options.push({
          value: item.id,
          label: item.equipment,
        })
      })
      return options
    }
    return []
  }, [equipment])

  const handleEquipmentSelect = useCallback(
    async (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = [...formik.values.equipment]
          newIds.push(changedValue)
          formik.setFieldValue('equipment', newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = formik.values.equipment.filter((id: string) => id !== changedValue)
          formik.setFieldValue('equipment', newIds)
          return
        }

        default:
          return
      }
    },
    [formik.values.equipment]
  )

  return (
    <SeaDrawer
      title={mode === DrawerMode.Create ? 'Add New Equipment Manual Document' : `Edit - ${selectedItem?.title}`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <DrawerPrimaryAction
          mode={mode}
          itemName={'Equipment Manual'}
          edit={{ onSubmit: formik.handleSubmit }}
          create={{ onSubmit: formik.handleSubmit }}
        />
      }>
      <DrawerContent>
        <DrawerRow>
          <SeaTextInput
            label={'Document Title'}
            showIcon={true}
            value={formik.values.title}
            onChangeText={formik.handleChange('title')}
            hasError={Boolean(formik.errors.title)}
            errorText={formik.errors.title}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaSelectInput
            label="Equipment"
            showIcon={true}
            data={equipmentOptions}
            isMulti
            style={{
              width: '100%',
            }}
            onItemSelect={handleEquipmentSelect}
            selectedItemValues={formik.values.equipment}
          />
        </DrawerRow>

        <DrawerRow>
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        </DrawerRow>
      </DrawerContent>
    </SeaDrawer>
  )
}
